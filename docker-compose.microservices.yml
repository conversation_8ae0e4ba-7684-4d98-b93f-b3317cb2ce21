version: '3.8'

networks:
  microservices-network:
    driver: bridge

volumes:
  redis-data:

services:
  # Redis for caching and message queuing
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    networks:
      - microservices-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - AUTH_SERVICE_URL=http://auth-service:3001
      - LINK_SERVICE_URL=http://link-service:3002
      - COMMUNITY_SERVICE_URL=http://community-service:3003
      - CHAT_SERVICE_URL=http://chat-service:3004
      - NEWS_SERVICE_URL=http://news-service:3005
      - ADMIN_SERVICE_URL=http://admin-service:3006
      - PHISHTANK_SERVICE_URL=http://phishtank-service:3007
      - CRIMINALIP_SERVICE_URL=http://criminalip-service:3008
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
      - auth-service
      - link-service
      - community-service
      - chat-service
      - news-service
      - admin-service
      - phishtank-service
      - criminalip-service
    networks:
      - microservices-network

  # Auth Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    networks:
      - microservices-network

  # Link Service
  link-service:
    build:
      context: ./services/link-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # Community Service
  community-service:
    build:
      context: ./services/community-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - PORT=3003
      - AUTH_SERVICE_URL=http://auth-service:3001
      - REDIS_URL=redis://redis:6379
    depends_on:
      - auth-service
      - redis
    networks:
      - microservices-network

  # Chat Service
  chat-service:
    build:
      context: ./services/chat-service
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=production
      - PORT=3004
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # News Service
  news-service:
    build:
      context: ./services/news-service
      dockerfile: Dockerfile
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      - PORT=3005
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # Admin Service
  admin-service:
    build:
      context: ./services/admin-service
      dockerfile: Dockerfile
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=production
      - PORT=3006
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COMMUNITY_SERVICE_URL=http://community-service:3003
      - LINK_SERVICE_URL=http://link-service:3002
    depends_on:
      - auth-service
      - community-service
      - link-service
    networks:
      - microservices-network

  # PhishTank Service
  phishtank-service:
    build:
      context: ./services/phishtank-service
      dockerfile: Dockerfile
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=production
      - PORT=3007
      - AUTH_SERVICE_URL=http://auth-service:3001
      - REDIS_URL=redis://redis:6379
    depends_on:
      - auth-service
      - redis
    networks:
      - microservices-network

  # CriminalIP Service
  criminalip-service:
    build:
      context: ./services/criminalip-service
      dockerfile: Dockerfile
    ports:
      - "3008:3008"
    environment:
      - NODE_ENV=production
      - PORT=3008
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - auth-service
    networks:
      - microservices-network

  # Frontend
  frontend:
    build:
      context: ./client
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
    depends_on:
      - api-gateway
    networks:
      - microservices-network
