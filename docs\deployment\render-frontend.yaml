# Render deployment configuration for factcheck-frontend
# Generated automatically - do not edit manually

services:
  - type: static
    name: factcheck-frontend
    env: static
    plan: free
    region: singapore
    rootDir: client
    buildCommand: npm install && npm run build
    staticPublishPath: ./build
    envVars:
      - key: REACT_APP_API_URL
        value: https://factcheck-api-gateway.onrender.com
      - key: REACT_APP_FIREBASE_API_KEY
        sync: false
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        sync: false
      - key: REACT_APP_FIREBASE_PROJECT_ID
        sync: false
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        sync: false
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        sync: false
      - key: REACT_APP_FIREBASE_APP_ID
        sync: false
      - key: GENERATE_SOURCEMAP
        value: false