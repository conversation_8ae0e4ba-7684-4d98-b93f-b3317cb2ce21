{"name": "api-gateway", "version": "1.0.0", "description": "API Gateway for Anti-Fraud Platform Microservices", "main": "src/app.js", "scripts": {"start": "node src/app.js", "start:full": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "test:contracts": "jest tests/contracts/ || echo \"No contract tests found\"", "test:contracts:auth": "jest tests/contracts/auth-service.contract.test.js", "test:contracts:link": "jest tests/contracts/link-service.contract.test.js", "test:contracts:community": "jest tests/contracts/community-service.contract.test.js", "test:contracts:all": "node ../shared/testing/contractTestRunner.js", "build": "echo 'No build step required for Node.js service'", "test:integration": "jest tests/integration/ || echo \"No integration tests found\"", "security:check": "npm audit --audit-level=moderate", "circuit-breaker:status": "curl -s http://localhost:8080/circuit-breaker/status || echo \"Circuit breaker not available\"", "test:unit": "jest tests/unit/ || npm test", "security:fix": "npm audit fix", "health:check": "curl -s http://localhost:8080/health || echo \"Service not running\"", "metrics": "curl -s http://localhost:8080/metrics || echo \"Metrics not available\"", "logs:tail": "tail -f logs/*.log || echo \"No logs found\"", "dev:debug": "NODE_ENV=development DEBUG=* npm start", "prod:start": "NODE_ENV=production npm start"}, "dependencies": {"axios": "^1.6.2", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-http-proxy": "^2.0.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "http-proxy-middleware": "^2.0.6", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "opossum": "^8.0.0", "prom-client": "^15.1.0", "redis": "^4.6.10", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@pact-foundation/pact": "^12.1.0", "@pact-foundation/pact-node": "^10.18.0", "@types/jest": "^29.5.8", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["api-gateway", "microservices", "proxy", "routing", "load-balancing", "authentication"], "author": "Anti-Fraud Platform Team", "license": "MIT", "config": {"techStack": {"circuitBreaker": true, "eventBus": true, "serviceAuth": true, "monitoring": true}}}