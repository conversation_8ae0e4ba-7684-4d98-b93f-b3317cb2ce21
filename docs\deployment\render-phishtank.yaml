# Render deployment configuration for factcheck-phishtank
# Generated automatically - do not edit manually

services:
  - type: web
    name: factcheck-phishtank
    env: node
    plan: free
    region: singapore
    rootDir: services/phishtank-service
    buildCommand: npm install
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: PORT
        value: "10000"
      - key: NODE_ENV
        value: production
      - key: JWT_SECRET
        sync: false
      - key: AUTH_SERVICE_URL
        value: https://factcheck-auth.onrender.com
      - key: PHISHTANK_API_KEY
        sync: false